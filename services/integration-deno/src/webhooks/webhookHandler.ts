import { Context } from "@oak/oak";
import { logger, logWebhookEvent } from "../utils/logger.ts";
import { errors } from "../middleware/errorHandler.ts";
import { ShopifyClient } from "../platforms/shopify/shopifyClient.ts";

export interface WebhookPayload {
  platform: string;
  topic: string;
  data: unknown;
  signature?: string;
  timestamp: string;
}

interface WebhookContext extends Context {
  params: {
    platform: string;
  };
}

export class WebhookHandler {
  /**
   * Handle incoming webhook from any platform
   */
  static async handleWebhook(ctx: WebhookContext): Promise<void> {
    const platform = ctx.params.platform;
    const topic = ctx.request.headers.get("x-shopify-topic") || 
                  ctx.request.headers.get("x-wc-webhook-topic") ||
                  "unknown";
    
    const signature = ctx.request.headers.get("x-shopify-hmac-sha256") ||
                     ctx.request.headers.get("x-wc-webhook-signature") ||
                     "";

    try {
      const body = await ctx.request.body.text();
      
      logWebhookEvent(platform, "webhook_received", {
        topic,
        hasSignature: !!signature,
        bodyLength: body.length,
      });

      // Verify webhook signature based on platform
      const isValid = await WebhookHandler.verifyWebhookSignature(platform, body, signature);
      
      if (!isValid) {
        logWebhookEvent(platform, "webhook_verification_failed", {
          topic,
          signature: signature.substring(0, 10) + "...",
        });
        throw errors.unauthorized("Invalid webhook signature");
      }

      // Parse webhook data
      const data = JSON.parse(body);
      
      const payload: WebhookPayload = {
        platform,
        topic,
        data,
        signature,
        timestamp: new Date().toISOString(),
      };

      // Process webhook based on platform and topic
      await WebhookHandler.processWebhook(payload);

      logWebhookEvent(platform, "webhook_processed", {
        topic,
        success: true,
      });

      ctx.response.status = 200;
      ctx.response.body = { success: true };
    } catch (error) {
      logWebhookEvent(platform, "webhook_processing_failed", {
        topic,
        error: (error as Error).message,
      });

      if (error instanceof SyntaxError) {
        throw errors.badRequest("Invalid JSON payload");
      }

      throw error;
    }
  }

  /**
   * Verify webhook signature based on platform
   */
  private static async verifyWebhookSignature(
    platform: string,
    body: string,
    signature: string,
  ): Promise<boolean> {
    if (!signature) {
      return false;
    }

    try {
      switch (platform.toLowerCase()) {
        case "shopify":
          const shopifySecret = Deno.env.get("SHOPIFY_WEBHOOK_SECRET");
          if (!shopifySecret) {
            logger.warn("Shopify webhook secret not configured");
            return false;
          }
          return await ShopifyClient.verifyWebhook(body, signature, shopifySecret);

        case "woocommerce":
          // TODO(@integration-team): Implement WooCommerce webhook verification
          return true;

        case "ebay":
          // TODO(@integration-team): Implement eBay webhook verification
          return true;

        default:
          logger.warn("Unknown platform for webhook verification", { platform });
          return false;
      }
    } catch (error) {
      logger.error("Webhook signature verification failed", {
        platform,
        error: (error as Error).message,
      });
      return false;
    }
  }

  /**
   * Process webhook based on platform and topic
   */
  private static async processWebhook(payload: WebhookPayload): Promise<void> {
    const { platform, topic, data } = payload;

    try {
      switch (platform.toLowerCase()) {
        case "shopify":
          await WebhookHandler.processShopifyWebhook(topic, data);
          break;

        case "woocommerce":
          await WebhookHandler.processWooCommerceWebhook(topic, data);
          break;

        case "ebay":
          await WebhookHandler.processEbayWebhook(topic, data);
          break;

        default:
          logger.warn("Unknown platform for webhook processing", { platform, topic });
      }
    } catch (error) {
      logger.error("Webhook processing failed", {
        platform,
        topic,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**
   * Process Shopify webhook
   */
  private static async processShopifyWebhook(topic: string, data: unknown): Promise<void> {
    switch (topic) {
      case "orders/create":
      case "orders/updated":
        await WebhookHandler.handleShopifyOrder(data, topic);
        break;

      case "orders/paid":
        await WebhookHandler.handleShopifyOrderPaid(data);
        break;

      case "orders/cancelled":
        await WebhookHandler.handleShopifyOrderCancelled(data);
        break;

      case "products/create":
      case "products/update":
        await WebhookHandler.handleShopifyProduct(data, topic);
        break;

      case "app/uninstalled":
        await WebhookHandler.handleShopifyAppUninstalled(data);
        break;

      default:
        logger.info("Unhandled Shopify webhook topic", { topic });
    }
  }

  /**
   * Handle Shopify order webhook
   */
  private static async handleShopifyOrder(data: unknown, topic: string): Promise<void> {
    const order = data as any;
    
    logger.info("Processing Shopify order webhook", {
      orderId: order.id,
      orderNumber: order.order_number,
      topic,
      totalPrice: order.total_price,
      currency: order.currency,
    });

    // TODO(@integration-team): Store/update order in database
    // const integrationService = getIntegrationService();
    // await integrationService.upsertOrder("shopify", order);

    // TODO(@integration-team): Send to analytics service
    // await analyticsClient.post("/api/events/order", {
    //   platform: "shopify",
    //   event: topic,
    //   order_id: order.id,
    //   order_number: order.order_number,
    //   total_price: parseFloat(order.total_price),
    //   currency: order.currency,
    //   customer_email: order.email,
    //   timestamp: order.created_at,
    // });
  }

  /**
   * Handle Shopify order paid webhook
   */
  private static async handleShopifyOrderPaid(data: unknown): Promise<void> {
    const order = data as any;
    
    logger.info("Processing Shopify order paid webhook", {
      orderId: order.id,
      orderNumber: order.order_number,
      totalPrice: order.total_price,
    });

    // TODO(@integration-team): Update order payment status
    // TODO(@integration-team): Trigger conversion tracking
  }

  /**
   * Handle Shopify order cancelled webhook
   */
  private static async handleShopifyOrderCancelled(data: unknown): Promise<void> {
    const order = data as any;
    
    logger.info("Processing Shopify order cancelled webhook", {
      orderId: order.id,
      orderNumber: order.order_number,
    });

    // TODO(@integration-team): Update order status
    // TODO(@integration-team): Handle refund tracking
  }

  /**
   * Handle Shopify product webhook
   */
  private static async handleShopifyProduct(data: unknown, topic: string): Promise<void> {
    const product = data as any;
    
    logger.info("Processing Shopify product webhook", {
      productId: product.id,
      title: product.title,
      topic,
    });

    // TODO(@integration-team): Store/update product in database
  }

  /**
   * Handle Shopify app uninstalled webhook
   */
  private static async handleShopifyAppUninstalled(data: unknown): Promise<void> {
    const shop = data as any;
    
    logger.info("Processing Shopify app uninstalled webhook", {
      shopDomain: shop.domain,
    });

    // TODO(@integration-team): Disable integration and clean up data
  }

  /**
   * Process WooCommerce webhook
   */
  private static async processWooCommerceWebhook(topic: string, data: unknown): Promise<void> {
    logger.info("Processing WooCommerce webhook", { topic });
    // TODO(@integration-team): Implement WooCommerce webhook processing
  }

  /**
   * Process eBay webhook
   */
  private static async processEbayWebhook(topic: string, data: unknown): Promise<void> {
    logger.info("Processing eBay webhook", { topic });
    // TODO(@integration-team): Implement eBay webhook processing
  }
}
