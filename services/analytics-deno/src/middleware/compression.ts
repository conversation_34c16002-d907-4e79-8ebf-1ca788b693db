import { Context, Middleware } from "@oak/oak";

/**
 * Simple compression middleware for Oak
 * Note: Oak doesn't have built-in compression like Express, so this is a basic implementation
 */
export const compressionMiddleware: Middleware = async (ctx: Context, next) => {
  await next();

  // Only compress if response has a body and is large enough
  if (!ctx.response.body || ctx.response.status === 204) {
    return;
  }

  const acceptEncoding = ctx.request.headers.get("accept-encoding") || "";
  const contentType = ctx.response.headers.get("content-type") || "";
  
  // Only compress text-based content types
  const compressibleTypes = [
    "application/json",
    "application/javascript",
    "text/",
    "application/xml",
    "application/rss+xml",
    "application/atom+xml",
  ];

  const shouldCompress = compressibleTypes.some(type => contentType.includes(type));
  
  if (!shouldCompress) {
    return;
  }

  // Check if client supports gzip
  if (acceptEncoding.includes("gzip")) {
    try {
      // Convert response body to string if it's an object
      let bodyString: string;
      if (typeof ctx.response.body === "string") {
        bodyString = ctx.response.body;
      } else if (ctx.response.body) {
        bodyString = JSON.stringify(ctx.response.body);
      } else {
        return;
      }

      // Only compress if body is large enough (> 1KB)
      if (bodyString.length < 1024) {
        return;
      }

      // Compress using gzip
      const encoder = new TextEncoder();
      const data = encoder.encode(bodyString);
      
      const compressed = await compressGzip(data);
      
      // Set compressed response
      ctx.response.body = compressed;
      ctx.response.headers.set("content-encoding", "gzip");
      ctx.response.headers.set("content-length", compressed.length.toString());
      ctx.response.headers.delete("content-length"); // Let Oak handle this
      
    } catch (error) {
      // If compression fails, just serve uncompressed
      console.warn("Compression failed:", error.message);
    }
  }
};

/**
 * Compress data using gzip
 */
async function compressGzip(data: Uint8Array): Promise<Uint8Array> {
  const stream = new CompressionStream("gzip");
  const writer = stream.writable.getWriter();
  const reader = stream.readable.getReader();
  
  // Write data to compression stream
  await writer.write(data);
  await writer.close();
  
  // Read compressed data
  const chunks: Uint8Array[] = [];
  let done = false;
  
  while (!done) {
    const { value, done: readerDone } = await reader.read();
    done = readerDone;
    if (value) {
      chunks.push(value);
    }
  }
  
  // Combine chunks
  const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
  const result = new Uint8Array(totalLength);
  let offset = 0;
  
  for (const chunk of chunks) {
    result.set(chunk, offset);
    offset += chunk.length;
  }
  
  return result;
}
